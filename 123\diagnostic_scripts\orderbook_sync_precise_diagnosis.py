#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 订单簿同步时间差精确诊断脚本
专门诊断143552.0ms时间差问题的根源

目标：精确定位订单簿时间戳不同步的原因
- 检查WebSocket数据时间戳格式
- 验证多交易所时间同步状态
- 分析时间戳标准化逻辑
- 定位143552.0ms具体来源
"""

import asyncio
import time
import os
import sys
import json
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class OrderbookSyncDiagnostic:
    """订单簿同步诊断器"""
    
    def __init__(self):
        self.results = {
            "diagnosis_time": datetime.now(timezone.utc).isoformat(),
            "target_symbol": "RESOLV-USDT",  # 从日志中发现的问题代币
            "target_exchanges": ["gate", "bybit", "okx"],
            "time_diff_threshold": 1000,  # 1000ms阈值
            "discovered_issues": [],
            "timestamp_analysis": {},
            "sync_status": {},
            "recommendations": []
        }
        
    async def run_comprehensive_diagnosis(self):
        """运行全面诊断"""
        print("🔥 开始订单簿同步时间差诊断...")
        print(f"目标代币: {self.results['target_symbol']}")
        print(f"目标交易所: {', '.join(self.results['target_exchanges'])}")
        print("=" * 80)
        
        # 1. 系统时间基线检查
        await self._diagnose_system_time_baseline()
        
        # 2. WebSocket时间戳格式诊断
        await self._diagnose_websocket_timestamp_formats()
        
        # 3. 交易所时间同步状态检查
        await self._diagnose_exchange_time_sync()
        
        # 4. 时间戳标准化逻辑验证
        await self._diagnose_timestamp_normalization()
        
        # 5. 143552.0ms具体来源分析
        await self._diagnose_specific_time_diff()
        
        # 6. 模拟真实场景测试
        await self._simulate_real_scenario()
        
        # 7. 生成诊断报告
        await self._generate_diagnosis_report()
        
    async def _diagnose_system_time_baseline(self):
        """诊断系统时间基线"""
        print("🔍 [诊断1] 系统时间基线检查...")
        
        try:
            # 获取多种时间格式
            current_time_float = time.time()
            current_time_ms = int(current_time_float * 1000)
            current_time_us = int(current_time_float * 1000000)
            current_time_ns = int(current_time_float * 1000000000)
            
            baseline = {
                "system_time_float": current_time_float,
                "system_time_ms": current_time_ms,
                "system_time_us": current_time_us,
                "system_time_ns": current_time_ns,
                "readable_time": datetime.fromtimestamp(current_time_float).strftime('%Y-%m-%d %H:%M:%S.%f'),
                "timezone": str(datetime.now().astimezone().tzinfo)
            }
            
            self.results["timestamp_analysis"]["system_baseline"] = baseline
            
            print(f"✅ 系统时间基线: {baseline['readable_time']}")
            print(f"   Float时间戳: {baseline['system_time_float']:.6f}")
            print(f"   毫秒时间戳: {baseline['system_time_ms']}")
            print(f"   时区: {baseline['timezone']}")
            
        except Exception as e:
            error = f"系统时间基线检查失败: {e}"
            self.results["discovered_issues"].append(error)
            print(f"❌ {error}")
    
    async def _diagnose_websocket_timestamp_formats(self):
        """诊断WebSocket时间戳格式"""
        print("\n🔍 [诊断2] WebSocket时间戳格式检查...")
        
        try:
            # 模拟不同交易所的WebSocket时间戳格式
            exchanges_timestamp_formats = {
                "gate": {
                    "typical_format": "seconds",
                    "example_value": time.time(),
                    "conversion_factor": 1000,
                    "description": "Gate.io通常使用秒级时间戳"
                },
                "bybit": {
                    "typical_format": "milliseconds", 
                    "example_value": int(time.time() * 1000),
                    "conversion_factor": 1,
                    "description": "Bybit通常使用毫秒级时间戳"
                },
                "okx": {
                    "typical_format": "milliseconds",
                    "example_value": int(time.time() * 1000),
                    "conversion_factor": 1,
                    "description": "OKX通常使用毫秒级时间戳"
                }
            }
            
            self.results["timestamp_analysis"]["exchange_formats"] = exchanges_timestamp_formats
            
            # 分析143552.0ms是否来自格式转换错误
            suspicious_diff = 143552.0
            current_time_ms = int(time.time() * 1000)
            
            # 检查是否是秒级时间戳被当作毫秒级处理
            potential_seconds_timestamp = suspicious_diff / 1000  # 143.552秒
            
            format_analysis = {
                "suspicious_time_diff_ms": suspicious_diff,
                "as_seconds": suspicious_diff / 1000,
                "as_minutes": suspicious_diff / 60000,
                "potential_causes": []
            }
            
            # 143.552秒 ≈ 2.4分钟，这很可能是真实的时间差
            if 60 < suspicious_diff / 1000 < 300:  # 1-5分钟范围
                format_analysis["potential_causes"].append("可能是WebSocket连接断开重连导致的时间延迟")
                format_analysis["potential_causes"].append("可能是某个交易所数据更新缓慢")
            
            self.results["timestamp_analysis"]["format_analysis"] = format_analysis
            
            print("📊 交易所时间戳格式分析:")
            for exchange, info in exchanges_timestamp_formats.items():
                print(f"   {exchange}: {info['description']}")
                print(f"   示例值: {info['example_value']}")
            
            print(f"\n🚨 问题分析: 143552.0ms ≈ {suspicious_diff/1000:.1f}秒 ≈ {suspicious_diff/60000:.1f}分钟")
            print("   这表明可能是真实的时间延迟，而非格式转换问题")
            
        except Exception as e:
            error = f"WebSocket时间戳格式检查失败: {e}"
            self.results["discovered_issues"].append(error)
            print(f"❌ {error}")
    
    async def _diagnose_exchange_time_sync(self):
        """诊断交易所时间同步状态"""
        print("\n🔍 [诊断3] 交易所时间同步状态检查...")
        
        try:
            # 尝试检查实际的WebSocket数据获取
            print("   检查WebSocket数据获取机制...")
            
            # 检查OpportunityScanner的market_data状态
            sync_status = {}
            
            try:
                from core.arbitrage_engine import get_arbitrage_engine
                engine = get_arbitrage_engine()
                
                if engine and hasattr(engine, 'opportunity_scanner') and engine.opportunity_scanner:
                    scanner = engine.opportunity_scanner
                    market_data = scanner.market_data
                    
                    # 分析RESOLV-USDT的数据
                    target_keys = [
                        "gate_spot_RESOLV-USDT",
                        "gate_futures_RESOLV-USDT", 
                        "bybit_spot_RESOLV-USDT",
                        "bybit_futures_RESOLV-USDT",
                        "okx_spot_RESOLV-USDT",
                        "okx_futures_RESOLV-USDT"
                    ]
                    
                    current_time_ms = time.time() * 1000
                    
                    for key in target_keys:
                        if key in market_data:
                            data = market_data[key]
                            if hasattr(data, 'timestamp'):
                                timestamp = data.timestamp
                                # 标准化时间戳为毫秒
                                if timestamp < 1e12:
                                    timestamp_ms = timestamp * 1000
                                else:
                                    timestamp_ms = timestamp
                                
                                age_ms = current_time_ms - timestamp_ms
                                
                                sync_status[key] = {
                                    "timestamp_raw": data.timestamp,
                                    "timestamp_ms": timestamp_ms,
                                    "age_ms": age_ms,
                                    "is_stale": age_ms > 5000,  # 5秒判断为过期
                                    "readable_time": datetime.fromtimestamp(timestamp_ms/1000).strftime('%H:%M:%S.%f')[:-3]
                                }
                    
                    self.results["sync_status"]["market_data_analysis"] = sync_status
                            
                else:
                    sync_status["scanner_status"] = "OpportunityScanner未初始化或不可用"
                    
            except ImportError:
                sync_status["import_error"] = "无法导入ArbitrageEngine"
            except Exception as e:
                sync_status["analysis_error"] = str(e)
            
            # 分析发现的时间差
            if sync_status:
                print("📊 市场数据时间戳分析:")
                stale_data_found = False
                max_age = 0
                
                for key, status in sync_status.items():
                    if isinstance(status, dict) and "age_ms" in status:
                        age = status["age_ms"]
                        max_age = max(max_age, age)
                        stale_indicator = "🚨过期" if status["is_stale"] else "✅新鲜"
                        print(f"   {key}: {status['readable_time']} (延迟{age:.1f}ms) {stale_indicator}")
                        
                        if status["is_stale"]:
                            stale_data_found = True
                
                if stale_data_found:
                    issue = f"发现过期数据，最大延迟: {max_age:.1f}ms"
                    self.results["discovered_issues"].append(issue)
                    print(f"🚨 {issue}")
                    
                    # 检查是否接近143552.0ms
                    if abs(max_age - 143552.0) < 1000:
                        critical_issue = f"发现与目标时间差接近的延迟: {max_age:.1f}ms ≈ 143552.0ms"
                        self.results["discovered_issues"].append(critical_issue)
                        print(f"🔥 关键发现: {critical_issue}")
                
            else:
                print("⚠️ 无法获取实时市场数据进行分析")
                self.results["discovered_issues"].append("无法获取实时市场数据")
                
        except Exception as e:
            error = f"交易所时间同步检查失败: {e}"
            self.results["discovered_issues"].append(error)
            print(f"❌ {error}")
    
    async def _diagnose_timestamp_normalization(self):
        """诊断时间戳标准化逻辑"""
        print("\n🔍 [诊断4] 时间戳标准化逻辑验证...")
        
        try:
            # 测试_normalize_timestamp_format函数
            from websocket.orderbook_validator import _normalize_timestamp_format
            
            # 测试各种时间戳格式
            test_cases = [
                ("当前秒级时间戳", time.time()),
                ("当前毫秒级时间戳", time.time() * 1000),
                ("过去的秒级时间戳", time.time() - 143.552),  # 143.552秒前
                ("过去的毫秒级时间戳", (time.time() - 143.552) * 1000),
                ("异常大的时间戳", time.time() * 1000000),  # 微秒级
            ]
            
            normalization_results = {}
            current_time_ms = time.time() * 1000
            
            for description, timestamp in test_cases:
                try:
                    normalized = _normalize_timestamp_format(timestamp)
                    diff_ms = abs(current_time_ms - normalized)
                    
                    normalization_results[description] = {
                        "original": timestamp,
                        "normalized": normalized,
                        "diff_from_now_ms": diff_ms,
                        "is_problematic": diff_ms > 100000,  # 超过100秒
                        "readable_original": datetime.fromtimestamp(timestamp if timestamp < 1e12 else timestamp/1000).strftime('%H:%M:%S.%f')[:-3],
                        "readable_normalized": datetime.fromtimestamp(normalized/1000).strftime('%H:%M:%S.%f')[:-3]
                    }
                    
                    # 检查是否接近143552.0ms
                    if abs(diff_ms - 143552.0) < 1000:
                        critical_finding = f"标准化测试发现接近目标的时间差: {description} -> {diff_ms:.1f}ms"
                        self.results["discovered_issues"].append(critical_finding)
                        print(f"🔥 关键发现: {critical_finding}")
                    
                except Exception as e:
                    normalization_results[description] = {
                        "error": str(e),
                        "original": timestamp
                    }
            
            self.results["timestamp_analysis"]["normalization_test"] = normalization_results
            
            print("📊 时间戳标准化测试结果:")
            for desc, result in normalization_results.items():
                if "error" not in result:
                    status = "🚨问题" if result["is_problematic"] else "✅正常"
                    print(f"   {desc}: {result['readable_original']} -> {result['readable_normalized']} (差异{result['diff_from_now_ms']:.1f}ms) {status}")
                else:
                    print(f"   {desc}: ❌错误 - {result['error']}")
            
        except Exception as e:
            error = f"时间戳标准化逻辑验证失败: {e}"
            self.results["discovered_issues"].append(error)
            print(f"❌ {error}")
    
    async def _diagnose_specific_time_diff(self):
        """诊断143552.0ms具体来源"""
        print("\n🔍 [诊断5] 143552.0ms具体来源分析...")
        
        try:
            target_diff = 143552.0
            
            # 分析这个时间差的可能含义
            analysis = {
                "target_time_diff_ms": target_diff,
                "as_seconds": target_diff / 1000,
                "as_minutes": target_diff / 60000,
                "as_hours": target_diff / 3600000,
                "possible_scenarios": []
            }
            
            # 143.552秒 ≈ 2.4分钟
            seconds = target_diff / 1000
            minutes = target_diff / 60000
            
            analysis["possible_scenarios"] = [
                {
                    "scenario": "WebSocket断线重连延迟",
                    "probability": "高",
                    "description": f"{seconds:.1f}秒的延迟可能来自WebSocket连接断开后重新连接的时间"
                },
                {
                    "scenario": "交易所服务器时间不同步",
                    "probability": "中",
                    "description": f"{minutes:.1f}分钟的差异可能是不同交易所服务器时间不同步"
                },
                {
                    "scenario": "数据缓存过期",
                    "probability": "高",
                    "description": f"某个交易所的订单簿数据缓存了{seconds:.1f}秒没有更新"
                },
                {
                    "scenario": "网络延迟累积",
                    "probability": "低",
                    "description": f"{seconds:.1f}秒的延迟对于网络通信来说过长，不太可能是简单网络延迟"
                }
            ]
            
            # 检查是否有特殊的时间戳模式
            current_time = time.time()
            historic_time = current_time - seconds
            
            # 检查143552这个数字是否有特殊含义
            number_analysis = {
                "decimal_representation": target_diff,
                "hex_representation": hex(int(target_diff)),
                "binary_pattern": bin(int(target_diff)),
                "factors": [],
                "is_round_number": target_diff % 1000 == 552,  # 检查是否是某种模式
            }
            
            # 寻找因数
            for i in range(2, int(target_diff**0.5) + 1):
                if int(target_diff) % i == 0:
                    number_analysis["factors"].append((i, int(target_diff) // i))
                    if len(number_analysis["factors"]) > 10:  # 限制因数数量
                        break
            
            analysis["number_analysis"] = number_analysis
            self.results["timestamp_analysis"]["specific_diff_analysis"] = analysis
            
            print(f"🎯 143552.0ms深度分析:")
            print(f"   等效时间: {seconds:.1f}秒 = {minutes:.2f}分钟")
            print(f"   十六进制: {number_analysis['hex_representation']}")
            print(f"   主要因数: {number_analysis['factors'][:5]}")
            
            print(f"\n📋 可能场景分析:")
            for scenario in analysis["possible_scenarios"]:
                print(f"   {scenario['scenario']} ({scenario['probability']}概率): {scenario['description']}")
            
            # 最可能的原因判断
            most_likely = "WebSocket断线重连延迟或数据缓存过期"
            self.results["recommendations"].append(f"最可能原因: {most_likely}")
            print(f"\n🔥 最可能原因: {most_likely}")
            
        except Exception as e:
            error = f"143552.0ms来源分析失败: {e}"
            self.results["discovered_issues"].append(error)
            print(f"❌ {error}")
    
    async def _simulate_real_scenario(self):
        """模拟真实场景测试"""
        print("\n🔍 [诊断6] 模拟真实场景测试...")
        
        try:
            # 模拟订单簿同步验证场景
            current_time_ms = time.time() * 1000
            
            # 场景1: 正常同步的订单簿
            normal_spot_orderbook = {
                "timestamp": current_time_ms,
                "asks": [[1.0, 100]],
                "bids": [[0.99, 100]]
            }
            
            normal_futures_orderbook = {
                "timestamp": current_time_ms - 100,  # 100ms延迟
                "asks": [[1.01, 100]],
                "bids": [[1.0, 100]]
            }
            
            # 场景2: 异常时间差的订单簿 (模拟143552.0ms问题)
            problematic_spot_orderbook = {
                "timestamp": current_time_ms,
                "asks": [[1.0, 100]],
                "bids": [[0.99, 100]]
            }
            
            problematic_futures_orderbook = {
                "timestamp": current_time_ms - 143552.0,  # 目标时间差
                "asks": [[1.01, 100]],
                "bids": [[1.0, 100]]
            }
            
            # 测试同步验证函数
            from websocket.orderbook_validator import validate_orderbook_synchronization
            
            scenarios = [
                ("正常同步场景", normal_spot_orderbook, normal_futures_orderbook),
                ("问题场景(143552ms)", problematic_spot_orderbook, problematic_futures_orderbook)
            ]
            
            simulation_results = {}
            
            for scenario_name, spot_book, futures_book in scenarios:
                try:
                    is_sync, error_msg = validate_orderbook_synchronization(
                        spot_book, futures_book, 
                        max_time_diff_ms=1000,
                        adaptive_threshold=True
                    )
                    
                    simulation_results[scenario_name] = {
                        "is_synchronized": is_sync,
                        "error_message": error_msg,
                        "spot_timestamp": spot_book["timestamp"],
                        "futures_timestamp": futures_book["timestamp"],
                        "calculated_diff_ms": abs(spot_book["timestamp"] - futures_book["timestamp"])
                    }
                    
                except Exception as e:
                    simulation_results[scenario_name] = {
                        "error": str(e),
                        "spot_timestamp": spot_book["timestamp"],
                        "futures_timestamp": futures_book["timestamp"]
                    }
            
            self.results["simulation_results"] = simulation_results
            
            print("📊 场景模拟结果:")
            for scenario, result in simulation_results.items():
                if "error" not in result:
                    status = "✅同步" if result["is_synchronized"] else "❌不同步"
                    print(f"   {scenario}: {status}")
                    if not result["is_synchronized"]:
                        print(f"     错误: {result['error_message']}")
                        print(f"     计算差异: {result['calculated_diff_ms']:.1f}ms")
                else:
                    print(f"   {scenario}: ❌测试错误 - {result['error']}")
            
        except Exception as e:
            error = f"真实场景模拟失败: {e}"
            self.results["discovered_issues"].append(error)
            print(f"❌ {error}")
    
    async def _generate_diagnosis_report(self):
        """生成诊断报告"""
        print("\n" + "=" * 80)
        print("📋 诊断报告生成...")
        
        # 汇总发现的问题
        issues_count = len(self.results["discovered_issues"])
        
        # 生成修复建议
        recommendations = [
            "检查WebSocket连接稳定性，确保连接不会频繁断开重连",
            "验证各交易所的时间戳格式一致性处理",
            "增加订单簿数据新鲜度监控，及时发现过期数据",
            "优化时间戳标准化逻辑，确保格式转换正确",
            "实施更智能的重试机制，处理临时性时间差问题"
        ]
        
        if issues_count > 0:
            recommendations.extend([
                f"优先处理发现的{issues_count}个具体问题",
                "增加实时监控日志，捕获时间差异常的具体时刻",
                "考虑实施自适应阈值，根据网络状况动态调整"
            ])
        
        self.results["recommendations"].extend(recommendations)
        
        # 保存诊断结果
        report_file = "orderbook_sync_diagnosis_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ 诊断报告已保存: {report_file}")
        print(f"🔍 发现问题数量: {issues_count}")
        print(f"💡 生成建议数量: {len(self.results['recommendations'])}")
        
        # 输出关键发现
        if issues_count > 0:
            print("\n🚨 关键发现:")
            for i, issue in enumerate(self.results["discovered_issues"], 1):
                print(f"   {i}. {issue}")
        
        print("\n💡 修复建议:")
        for i, rec in enumerate(self.results["recommendations"], 1):
            print(f"   {i}. {rec}")
        
        print("\n" + "=" * 80)
        print("🎯 诊断结论:")
        print("143552.0ms (≈2.4分钟) 的时间差很可能来自:")
        print("1. WebSocket连接断开重连导致的数据延迟")
        print("2. 某个交易所的订单簿数据缓存过期")
        print("3. 网络不稳定导致的数据更新延迟")
        print("")
        print("建议重点检查WebSocket连接稳定性和数据更新机制!")
        print("=" * 80)

async def main():
    """主函数"""
    diagnostic = OrderbookSyncDiagnostic()
    await diagnostic.run_comprehensive_diagnosis()

if __name__ == "__main__":
    asyncio.run(main())