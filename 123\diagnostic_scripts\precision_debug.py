#!/usr/bin/env python3
"""
精度问题快速诊断
验证ICNT-USDT的qty_precision设置
"""

import os
import sys
import asyncio
from decimal import Decimal

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def diagnose_precision_issue():
    """诊断精度问题"""
    try:
        # 导入预加载器
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        # 获取ICNT-USDT期货的交易规则
        rule = preloader.get_trading_rule("bybit", "ICNT-USDT", "futures")
        
        if rule:
            print(f"[成功] 找到ICNT-USDT期货规则:")
            print(f"  qty_step: {rule.qty_step}")
            print(f"  qty_precision: {rule.qty_precision}")
            print(f"  price_step: {rule.price_step}")
            print(f"  price_precision: {rule.price_precision}")
            
            # 测试166.904的处理
            test_amount = 166.904
            
            # 步骤1: 截取逻辑
            amount_decimal = Decimal(str(test_amount))
            step_decimal = Decimal(str(rule.qty_step))
            adjusted = (amount_decimal // step_decimal) * step_decimal
            
            print(f"\n[截取测试]:")
            print(f"  输入: {test_amount}")
            print(f"  步长: {rule.qty_step}")
            print(f"  截取后: {float(adjusted)}")
            
            # 步骤2: 格式化逻辑
            formatted = f"{float(adjusted):.{rule.qty_precision}f}"
            
            print(f"\n[格式化测试]:")
            print(f"  截取值: {float(adjusted)}")
            print(f"  精度位数: {rule.qty_precision}")
            print(f"  格式化后: '{formatted}'")
            
            # 步骤3: 问题验证
            if formatted == "166.9":
                print(f"\n[问题确认] qty_precision={rule.qty_precision} 导致精度损失!")
                print(f"  建议修复: qty_precision应该至少为3位小数")
            else:
                print(f"\n[问题排除] 格式化结果正常: '{formatted}'")
                
        else:
            print(f"[错误] 未找到ICNT-USDT期货规则")
            
    except Exception as e:
        print(f"[异常] 诊断失败: {e}")

if __name__ == "__main__":
    asyncio.run(diagnose_precision_issue())