#!/usr/bin/env python3
"""
机构级综合修复验证测试
验证所有修复的完整性和一致性
"""

import os
import sys
import asyncio
import json
import time
from decimal import Decimal

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def comprehensive_validation():
    """机构级综合验证测试"""
    test_results = {
        "overall_status": "PENDING",
        "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "tests": []
    }
    
    try:
        print("=" * 80)
        print("机构级综合修复验证测试")
        print("=" * 80)
        
        # 测试1: API数据优先修复验证
        print("\n[测试1] API数据优先修复验证...")
        api_test_result = await test_api_data_priority()
        test_results["tests"].append(api_test_result)
        
        # 测试2: ICNT-USDT具体修复验证
        print("\n[测试2] ICNT-USDT具体修复验证...")
        icnt_test_result = await test_icnt_usdt_fix()
        test_results["tests"].append(icnt_test_result)
        
        # 测试3: 交易所格式化一致性验证
        print("\n[测试3] 交易所格式化一致性验证...")
        consistency_test_result = await test_exchange_consistency()
        test_results["tests"].append(consistency_test_result)
        
        # 测试4: 现货期货处理逻辑验证
        print("\n[测试4] 现货期货处理逻辑验证...")
        market_test_result = await test_market_type_logic()
        test_results["tests"].append(market_test_result)
        
        # 测试5: 边界情况处理验证
        print("\n[测试5] 边界情况处理验证...")
        edge_case_result = await test_edge_cases()
        test_results["tests"].append(edge_case_result)
        
        # 计算总体结果
        passed_tests = sum(1 for test in test_results["tests"] if test["status"] == "PASSED")
        total_tests = len(test_results["tests"])
        
        if passed_tests == total_tests:
            test_results["overall_status"] = "PASSED"
            print(f"\nOK 所有测试通过! ({passed_tests}/{total_tests})")
        else:
            test_results["overall_status"] = "FAILED"
            print(f"\nERROR 部分测试失败! ({passed_tests}/{total_tests})")
            
        # 输出测试结果到JSON文件
        result_file = "diagnostic_results/comprehensive_fix_validation.json"
        os.makedirs(os.path.dirname(result_file), exist_ok=True)
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, indent=2, ensure_ascii=False)
            
        print(f"\nTEST_REPORT 详细测试结果已保存到: {result_file}")
        return test_results
        
    except Exception as e:
        test_results["overall_status"] = "ERROR"
        test_results["error"] = str(e)
        print(f"[异常] 综合验证失败: {e}")
        import traceback
        traceback.print_exc()
        return test_results

async def test_api_data_priority():
    """测试API数据优先修复"""
    try:
        print("  - 清理ICNT-USDT缓存...")
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        cache_key = "bybit_ICNT-USDT_futures"
        if cache_key in preloader.trading_rules:
            del preloader.trading_rules[cache_key]
            
        # 强制重新获取规则
        rule = preloader.get_trading_rule("bybit", "ICNT-USDT", "futures")
        
        if rule and rule.source == "api" and float(rule.qty_step) == 1.0:
            print("  OK API数据优先修复成功")
            return {
                "name": "API数据优先修复",
                "status": "PASSED",
                "details": {
                    "step_size": float(rule.qty_step),
                    "source": rule.source,
                    "expected_step_size": 1.0,
                    "expected_source": "api"
                }
            }
        else:
            print(f"  ERROR API数据优先修复失败: step_size={rule.qty_step if rule else 'None'}, source={rule.source if rule else 'None'}")
            return {
                "name": "API数据优先修复", 
                "status": "FAILED",
                "details": {
                    "step_size": float(rule.qty_step) if rule else None,
                    "source": rule.source if rule else None,
                    "expected_step_size": 1.0,
                    "expected_source": "api"
                }
            }
            
    except Exception as e:
        print(f"  ERROR API数据优先测试异常: {e}")
        return {"name": "API数据优先修复", "status": "ERROR", "error": str(e)}

async def test_icnt_usdt_fix():
    """测试ICNT-USDT具体修复"""
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        test_cases = [
            {"input": 166.904, "expected": "166"},
            {"input": 100.5, "expected": "100"}, 
            {"input": 0.9, "expected": "0"},
            {"input": 250.0, "expected": "250"}
        ]
        
        results = []
        for case in test_cases:
            formatted = preloader.format_amount_unified(case["input"], "bybit", "ICNT-USDT", "futures")
            truncated = preloader.truncate_to_step_size(case["input"], "bybit", "ICNT-USDT", "futures")
            
            is_correct = formatted == case["expected"]
            results.append({
                "input": case["input"],
                "formatted": formatted,
                "truncated": truncated,
                "expected": case["expected"],
                "correct": is_correct
            })
            
            status = "OK" if is_correct else "ERROR"
            print(f"  {status} {case['input']} → '{formatted}' (期望: '{case['expected']}')")
            
        all_correct = all(r["correct"] for r in results)
        
        return {
            "name": "ICNT-USDT具体修复",
            "status": "PASSED" if all_correct else "FAILED",
            "details": {"test_cases": results}
        }
        
    except Exception as e:
        print(f"  ERROR ICNT-USDT修复测试异常: {e}")
        return {"name": "ICNT-USDT具体修复", "status": "ERROR", "error": str(e)}

async def test_exchange_consistency():
    """测试交易所格式化一致性"""
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        # 测试现货格式化一致性
        test_amount = 100.123456789
        test_symbol = "BTC-USDT"
        
        spot_results = {}
        for exchange in ["bybit", "gate", "okx"]:
            try:
                formatted = preloader.format_amount_unified(test_amount, exchange, test_symbol, "spot")
                spot_results[exchange] = formatted
                print(f"  现货 {exchange}: {test_amount} → '{formatted}'")
            except Exception as e:
                spot_results[exchange] = f"ERROR: {e}"
                print(f"  现货 {exchange}: ERROR - {e}")
        
        # 测试期货格式化一致性  
        futures_results = {}
        for exchange in ["bybit", "gate", "okx"]:
            try:
                formatted = preloader.format_amount_unified(test_amount, exchange, test_symbol, "futures")
                futures_results[exchange] = formatted
                print(f"  期货 {exchange}: {test_amount} → '{formatted}'")
            except Exception as e:
                futures_results[exchange] = f"ERROR: {e}"
                print(f"  期货 {exchange}: ERROR - {e}")
        
        # 检查一致性（格式相似即可，步长可能不同）
        spot_success = all("ERROR" not in str(v) for v in spot_results.values())
        futures_success = all("ERROR" not in str(v) for v in futures_results.values())
        
        overall_success = spot_success and futures_success
        
        print(f"  {'OK' if overall_success else 'ERROR'} 交易所格式化一致性: {'通过' if overall_success else '失败'}")
        
        return {
            "name": "交易所格式化一致性",
            "status": "PASSED" if overall_success else "FAILED",
            "details": {
                "spot_results": spot_results,
                "futures_results": futures_results
            }
        }
        
    except Exception as e:
        print(f"  ERROR 交易所一致性测试异常: {e}")
        return {"name": "交易所格式化一致性", "status": "ERROR", "error": str(e)}

async def test_market_type_logic():
    """测试现货期货处理逻辑"""
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        test_cases = [
            {"exchange": "bybit", "symbol": "BTC-USDT", "market": "spot"},
            {"exchange": "bybit", "symbol": "BTC-USDT", "market": "futures"},
            {"exchange": "gate", "symbol": "ETH-USDT", "market": "spot"},
            {"exchange": "okx", "symbol": "DOT-USDT", "market": "spot"}
        ]
        
        results = []
        for case in test_cases:
            try:
                test_amount = 1.234567
                formatted = preloader.format_amount_unified(
                    test_amount, case["exchange"], case["symbol"], case["market"]
                )
                
                results.append({
                    "exchange": case["exchange"],
                    "symbol": case["symbol"], 
                    "market": case["market"],
                    "input": test_amount,
                    "output": formatted,
                    "success": True
                })
                
                print(f"  OK {case['exchange']} {case['market']}: {test_amount} → '{formatted}'")
                
            except Exception as e:
                results.append({
                    "exchange": case["exchange"],
                    "symbol": case["symbol"],
                    "market": case["market"],
                    "input": test_amount,
                    "error": str(e),
                    "success": False
                })
                print(f"  ERROR {case['exchange']} {case['market']}: ERROR - {e}")
        
        success_rate = sum(1 for r in results if r["success"]) / len(results)
        overall_success = success_rate >= 0.8  # 80%成功率
        
        print(f"  {'OK' if overall_success else 'ERROR'} 现货期货逻辑: {success_rate*100:.1f}%成功率")
        
        return {
            "name": "现货期货处理逻辑",
            "status": "PASSED" if overall_success else "FAILED", 
            "details": {
                "success_rate": success_rate,
                "test_cases": results
            }
        }
        
    except Exception as e:
        print(f"  ERROR 现货期货逻辑测试异常: {e}")
        return {"name": "现货期货处理逻辑", "status": "ERROR", "error": str(e)}

async def test_edge_cases():
    """测试边界情况处理"""
    try:
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        edge_cases = [
            {"amount": 0.0, "desc": "零值"},
            {"amount": 0.000001, "desc": "极小值"},
            {"amount": 999999.999999, "desc": "极大值"},
            {"amount": 1.0, "desc": "整数值"},
            {"amount": 0.1, "desc": "一位小数"},
            {"amount": 123.456789012345, "desc": "高精度小数"}
        ]
        
        results = []
        for case in edge_cases:
            try:
                formatted = preloader.format_amount_unified(case["amount"], "bybit", "BTC-USDT", "spot")
                truncated = preloader.truncate_to_step_size(case["amount"], "bybit", "BTC-USDT", "spot")
                
                # 验证格式化结果的合理性
                is_valid = (
                    isinstance(formatted, str) and
                    len(formatted) > 0 and
                    formatted != "nan" and
                    formatted != "inf"
                )
                
                results.append({
                    "amount": case["amount"],
                    "description": case["desc"],
                    "formatted": formatted,
                    "truncated": truncated,
                    "valid": is_valid
                })
                
                status = "OK" if is_valid else "ERROR"
                print(f"  {status} {case['desc']}: {case['amount']} → '{formatted}'")
                
            except Exception as e:
                results.append({
                    "amount": case["amount"],
                    "description": case["desc"],
                    "error": str(e),
                    "valid": False
                })
                print(f"  ERROR {case['desc']}: {case['amount']} → ERROR: {e}")
        
        valid_rate = sum(1 for r in results if r["valid"]) / len(results)
        overall_success = valid_rate >= 0.9  # 90%有效率
        
        print(f"  {'OK' if overall_success else 'ERROR'} 边界情况处理: {valid_rate*100:.1f}%有效率")
        
        return {
            "name": "边界情况处理",
            "status": "PASSED" if overall_success else "FAILED",
            "details": {
                "valid_rate": valid_rate,
                "edge_cases": results
            }
        }
        
    except Exception as e:
        print(f"  ERROR 边界情况测试异常: {e}")
        return {"name": "边界情况处理", "status": "ERROR", "error": str(e)}

if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    
    asyncio.run(comprehensive_validation())