#!/usr/bin/env python3
"""
交易规则对比诊断
对比预加载器存储的规则 vs Bybit API实际返回
"""

import os
import sys
import asyncio
import json

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def compare_trading_rules():
    """对比交易规则存储 vs API实际返回"""
    try:
        print("[开始] 交易规则对比诊断...")
        
        # 1. 检查预加载器中存储的规则
        print("\n=== 预加载器存储的规则 ===")
        from core.trading_rules_preloader import get_trading_rules_preloader
        preloader = get_trading_rules_preloader()
        
        rule = preloader.get_trading_rule("bybit", "ICNT-USDT", "futures")
        if rule:
            print(f"预加载器规则:")
            print(f"  symbol: {rule.symbol}")
            print(f"  exchange: {rule.exchange}")
            print(f"  market_type: {rule.market_type}")
            print(f"  qty_step: {rule.qty_step}")
            print(f"  price_step: {rule.price_step}")
            print(f"  qty_precision: {rule.qty_precision}")
            print(f"  price_precision: {rule.price_precision}")
            print(f"  min_qty: {rule.min_qty}")
            print(f"  max_qty: {rule.max_qty}")
            print(f"  source: {rule.source}")
            print(f"  timestamp: {rule.timestamp}")
        else:
            print("ERROR 预加载器中未找到规则")
            
        # 2. 直接查询API获取规则
        print("\n=== Bybit API实际返回 ===")
        from exchanges.bybit_exchange import BybitExchange
        
        api_key = os.getenv("BYBIT_API_KEY", "")
        api_secret = os.getenv("BYBIT_API_SECRET", "")
        
        if api_key and api_secret:
            exchange = BybitExchange(api_key, api_secret)
            
            # 直接调用预加载器的API获取方法
            precision_info = await preloader._get_precision_from_exchange_api(
                exchange, "ICNT-USDT", "futures"
            )
            
            print(f"API精度信息:")
            print(json.dumps(precision_info, indent=2, ensure_ascii=False) if precision_info else "None")
            
            await exchange.close()
        else:
            print("ERROR 未设置API密钥")
            
        # 3. 测试问题数量的处理
        print("\n=== 问题数量处理测试 ===")
        if rule:
            test_amount = 166.904
            
            # 使用预加载器方法处理
            formatted = preloader.format_amount_unified(test_amount, "bybit", "ICNT-USDT", "futures")
            truncated = preloader.truncate_to_step_size(test_amount, "bybit", "ICNT-USDT", "futures")
            
            print(f"输入: {test_amount}")
            print(f"format_amount_unified: '{formatted}'")
            print(f"truncate_to_step_size: {truncated}")
            
            # 手动验证
            from decimal import Decimal
            amount_decimal = Decimal(str(test_amount))
            step_decimal = Decimal(str(rule.qty_step))
            manual_truncated = (amount_decimal // step_decimal) * step_decimal
            
            print(f"手动截取计算: {float(manual_truncated)}")
            print(f"是否步长整数倍: {test_amount % float(rule.qty_step) == 0}")
            
    except Exception as e:
        print(f"[异常] 诊断失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    
    asyncio.run(compare_trading_rules())