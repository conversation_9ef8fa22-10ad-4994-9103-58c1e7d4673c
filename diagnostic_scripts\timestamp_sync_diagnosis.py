#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔥 时间戳同步问题精确诊断脚本
专门用于诊断143552.0ms这类巨大时间差问题的根本原因

根据07B修复记录文档，此类问题历史上出现过多次：
- 79373.0ms时间差问题
- 148977.0ms时间差问题  
- 253774.0ms时间差问题
- 当前: 143552.0ms时间差问题

本脚本按照修复提示词要求进行精准诊断
"""

import os
import sys
import time
import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append('/root/myproject/123/65B 修复了 日志/123')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] [%(name)s] %(message)s'
)
logger = logging.getLogger(__name__)

class TimestampSyncDiagnostic:
    """时间戳同步问题精确诊断器"""
    
    def __init__(self):
        self.exchanges = ["gate", "bybit", "okx"]
        self.diagnosis_results = {}
        self.critical_issues = []
        
    async def run_comprehensive_diagnosis(self) -> Dict[str, Any]:
        """运行全面诊断"""
        logger.info("🔍 开始时间戳同步问题全面诊断...")
        
        # 诊断步骤1：统一时间戳处理器诊断
        await self._diagnose_unified_timestamp_processor()
        
        # 诊断步骤2：WebSocket时间戳提取诊断
        await self._diagnose_websocket_timestamp_extraction()
        
        # 诊断步骤3：订单簿验证逻辑诊断
        await self._diagnose_orderbook_validation_logic()
        
        # 诊断步骤4：跨交易所时间同步诊断
        await self._diagnose_cross_exchange_sync()
        
        # 诊断步骤5：历史问题模式分析
        self._analyze_historical_patterns()
        
        # 生成诊断报告
        return self._generate_diagnosis_report()
    
    async def _diagnose_unified_timestamp_processor(self):
        """诊断统一时间戳处理器"""
        logger.info("📊 诊断步骤1: 统一时间戳处理器")
        
        try:
            from websocket.unified_timestamp_processor import (
                get_timestamp_processor, 
                check_all_timestamp_sync_health,
                initialize_all_timestamp_processors
            )
            
            # 1.1 检查处理器初始化状态
            processor_status = {}
            for exchange in self.exchanges:
                try:
                    processor = get_timestamp_processor(exchange)
                    status = processor.get_sync_status()
                    processor_status[exchange] = status
                    
                    # 检查关键指标
                    if not status['time_synced']:
                        self.critical_issues.append(f"{exchange}时间未同步")
                    
                    if abs(status['time_offset_ms']) > 5000:
                        self.critical_issues.append(f"{exchange}时间偏移过大: {status['time_offset_ms']}ms")
                    
                    if status['sync_age_seconds'] > 300:  # 超过5分钟
                        self.critical_issues.append(f"{exchange}时间同步过期: {status['sync_age_seconds']}秒")
                        
                except Exception as e:
                    self.critical_issues.append(f"{exchange}处理器异常: {str(e)}")
                    processor_status[exchange] = {"error": str(e)}
            
            # 1.2 检查全局健康状态
            try:
                health_status = await check_all_timestamp_sync_health()
                self.diagnosis_results["processor_health"] = health_status
            except Exception as e:
                self.critical_issues.append(f"健康检查失败: {str(e)}")
            
            self.diagnosis_results["processor_status"] = processor_status
            logger.info(f"✅ 处理器诊断完成，发现{len([k for k, v in processor_status.items() if 'error' in v])}个异常")
            
        except ImportError as e:
            self.critical_issues.append(f"无法导入统一时间戳处理器: {str(e)}")
        except Exception as e:
            self.critical_issues.append(f"处理器诊断异常: {str(e)}")
    
    async def _diagnose_websocket_timestamp_extraction(self):
        """诊断WebSocket时间戳提取逻辑"""
        logger.info("📊 诊断步骤2: WebSocket时间戳提取")
        
        extraction_issues = {}
        
        # 2.1 检查各交易所WebSocket客户端的时间戳提取逻辑
        for exchange in self.exchanges:
            issues = []
            
            try:
                # 检查WebSocket客户端是否存在
                if exchange == "gate":
                    from websocket.gate_ws import GateWebSocketClient
                    ws_class = GateWebSocketClient
                elif exchange == "bybit":
                    from websocket.bybit_ws import BybitWebSocketClient  
                    ws_class = BybitWebSocketClient
                elif exchange == "okx":
                    from websocket.okx_ws import OKXWebSocketClient
                    ws_class = OKXWebSocketClient
                
                # 检查关键方法是否存在
                if not hasattr(ws_class, '_process_orderbook_data'):
                    issues.append("缺少_process_orderbook_data方法")
                
                # 检查时间戳处理相关属性
                instance_check = {
                    "heartbeat_interval": "心跳间隔配置",
                    "connection_timeout": "连接超时配置"
                }
                
                for attr, desc in instance_check.items():
                    if not hasattr(ws_class, attr):
                        issues.append(f"缺少{desc}: {attr}")
                
            except ImportError as e:
                issues.append(f"无法导入WebSocket客户端: {str(e)}")
            except Exception as e:
                issues.append(f"检查异常: {str(e)}")
            
            if issues:
                extraction_issues[exchange] = issues
                self.critical_issues.extend([f"{exchange}: {issue}" for issue in issues])
        
        self.diagnosis_results["websocket_extraction"] = extraction_issues
        logger.info(f"✅ WebSocket时间戳提取诊断完成，发现{len(extraction_issues)}个交易所存在问题")
    
    async def _diagnose_orderbook_validation_logic(self):
        """诊断订单簿验证逻辑"""
        logger.info("📊 诊断步骤3: 订单簿验证逻辑")
        
        try:
            from websocket.orderbook_validator import validate_orderbook_synchronization
            
            # 3.1 模拟143552.0ms时间差场景
            current_time = int(time.time() * 1000)
            spot_orderbook = {
                "timestamp": current_time,
                "asks": [[50000.0, 1.0]],
                "bids": [[49900.0, 1.0]]
            }
            futures_orderbook = {
                "timestamp": current_time - 143552,  # 模拟143552ms时间差
                "asks": [[50100.0, 1.0]], 
                "bids": [[50000.0, 1.0]]
            }
            
            # 3.2 测试不同阈值下的验证结果
            validation_results = {}
            test_thresholds = [800, 1000, 5000, 10000, 150000]  # 不同阈值测试
            
            for threshold in test_thresholds:
                try:
                    is_sync, error_msg = validate_orderbook_synchronization(
                        spot_orderbook, futures_orderbook, 
                        max_time_diff_ms=threshold,
                        adaptive_threshold=True
                    )
                    validation_results[f"{threshold}ms"] = {
                        "synchronized": is_sync,
                        "error_message": error_msg
                    }
                except Exception as e:
                    validation_results[f"{threshold}ms"] = {
                        "synchronized": False,
                        "error_message": f"验证异常: {str(e)}"
                    }
            
            # 3.3 检查自适应阈值机制
            try:
                is_sync_adaptive, error_adaptive = validate_orderbook_synchronization(
                    spot_orderbook, futures_orderbook,
                    max_time_diff_ms=800,
                    adaptive_threshold=True
                )
                validation_results["adaptive_800ms"] = {
                    "synchronized": is_sync_adaptive,
                    "error_message": error_adaptive
                }
            except Exception as e:
                validation_results["adaptive_800ms"] = {
                    "synchronized": False,
                    "error_message": f"自适应验证异常: {str(e)}"
                }
            
            self.diagnosis_results["orderbook_validation"] = validation_results
            
            # 记录关键发现
            if not validation_results.get("800ms", {}).get("synchronized", False):
                self.critical_issues.append("800ms阈值下143552ms时间差验证失败(预期)")
            
            logger.info("✅ 订单簿验证逻辑诊断完成")
            
        except ImportError as e:
            self.critical_issues.append(f"无法导入订单簿验证器: {str(e)}")
        except Exception as e:
            self.critical_issues.append(f"订单簿验证诊断异常: {str(e)}")
    
    async def _diagnose_cross_exchange_sync(self):
        """诊断跨交易所时间同步"""
        logger.info("📊 诊断步骤4: 跨交易所时间同步")
        
        try:
            from websocket.unified_timestamp_processor import get_timestamp_processor
            
            # 4.1 获取所有交易所的当前时间戳
            exchange_timestamps = {}
            for exchange in self.exchanges:
                try:
                    processor = get_timestamp_processor(exchange)
                    timestamp = processor.get_synced_timestamp()
                    exchange_timestamps[exchange] = {
                        "timestamp": timestamp,
                        "readable_time": datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
                    }
                except Exception as e:
                    exchange_timestamps[exchange] = {"error": str(e)}
            
            # 4.2 计算跨交易所时间差
            timestamps = [v["timestamp"] for v in exchange_timestamps.values() if "timestamp" in v]
            if len(timestamps) >= 2:
                max_diff = max(timestamps) - min(timestamps)
                avg_timestamp = sum(timestamps) / len(timestamps)
                
                cross_sync_analysis = {
                    "exchange_timestamps": exchange_timestamps,
                    "max_time_difference_ms": max_diff,
                    "average_timestamp": avg_timestamp,
                    "sync_quality": "GOOD" if max_diff < 1000 else "WARNING" if max_diff < 5000 else "CRITICAL"
                }
                
                if max_diff > 10000:
                    self.critical_issues.append(f"跨交易所时间差过大: {max_diff:.1f}ms")
                
            else:
                cross_sync_analysis = {
                    "error": "无法获取足够的交易所时间戳进行对比",
                    "available_exchanges": len(timestamps)
                }
                self.critical_issues.append("跨交易所时间同步检查失败")
            
            self.diagnosis_results["cross_exchange_sync"] = cross_sync_analysis
            logger.info("✅ 跨交易所时间同步诊断完成")
            
        except Exception as e:
            self.critical_issues.append(f"跨交易所同步诊断异常: {str(e)}")
    
    def _analyze_historical_patterns(self):
        """分析历史问题模式"""
        logger.info("📊 诊断步骤5: 历史问题模式分析")
        
        # 历史时间差问题记录（从07B文档获取）
        historical_issues = [
            {"timestamp_diff": 79373.0, "date": "2025-07-29", "status": "已修复"},
            {"timestamp_diff": 148977.0, "date": "2025-07-29", "status": "已修复"}, 
            {"timestamp_diff": 253774.0, "date": "2025-07-29", "status": "已修复"},
            {"timestamp_diff": 143552.0, "date": "当前", "status": "待修复"}
        ]
        
        # 模式分析
        patterns = {
            "average_diff_ms": sum(issue["timestamp_diff"] for issue in historical_issues) / len(historical_issues),
            "max_diff_ms": max(issue["timestamp_diff"] for issue in historical_issues),
            "min_diff_ms": min(issue["timestamp_diff"] for issue in historical_issues),
            "recurrence_frequency": "高频", # 多次出现类似问题
            "typical_range": "79-254秒",
            "current_issue_position": "中等偏上"
        }
        
        # 可能的共同根因分析
        common_root_causes = [
            "WebSocket连接静默断开但程序未检测到",
            "时间戳字段映射错误或数据源混用",
            "服务器时间API访问失败导致回退机制问题",
            "SSL证书问题影响时间同步",
            "网络延迟或VPS环境时间同步问题"
        ]
        
        self.diagnosis_results["historical_analysis"] = {
            "historical_issues": historical_issues,
            "patterns": patterns,
            "common_root_causes": common_root_causes,
            "fix_priority": "HIGH - 基于历史经验，此类问题严重影响套利效率"
        }
        
        logger.info("✅ 历史问题模式分析完成")
    
    def _generate_diagnosis_report(self) -> Dict[str, Any]:
        """生成诊断报告"""
        logger.info("📋 生成综合诊断报告...")
        
        report = {
            "diagnosis_timestamp": datetime.now().isoformat(),
            "target_issue": "订单簿同步验证失败: 时间差143552.0ms > 1000ms",
            "diagnosis_results": self.diagnosis_results,
            "critical_issues": self.critical_issues,
            "total_issues_found": len(self.critical_issues),
            "diagnosis_summary": {
                "unified_processor_status": "正在检查" if "processor_status" in self.diagnosis_results else "检查失败",
                "websocket_extraction_status": "正在检查" if "websocket_extraction" in self.diagnosis_results else "检查失败", 
                "orderbook_validation_status": "正在检查" if "orderbook_validation" in self.diagnosis_results else "检查失败",
                "cross_exchange_sync_status": "正在检查" if "cross_exchange_sync" in self.diagnosis_results else "检查失败"
            }
        }
        
        # 优先级建议
        if len(self.critical_issues) > 10:
            report["severity"] = "CRITICAL"
            report["action_priority"] = "IMMEDIATE"
        elif len(self.critical_issues) > 5:
            report["severity"] = "HIGH" 
            report["action_priority"] = "URGENT"
        else:
            report["severity"] = "MEDIUM"
            report["action_priority"] = "NORMAL"
        
        # 修复建议
        report["fix_recommendations"] = self._generate_fix_recommendations()
        
        return report
    
    def _generate_fix_recommendations(self) -> List[str]:
        """生成修复建议"""
        recommendations = []
        
        # 基于诊断结果生成具体建议
        if "processor_status" in self.diagnosis_results:
            processor_issues = [k for k, v in self.diagnosis_results["processor_status"].items() if "error" in v]
            if processor_issues:
                recommendations.append(f"修复{len(processor_issues)}个交易所的时间戳处理器问题")
        
        if "websocket_extraction" in self.diagnosis_results:
            extraction_issues = self.diagnosis_results["websocket_extraction"]
            if extraction_issues:
                recommendations.append(f"修复{len(extraction_issues)}个交易所的WebSocket时间戳提取问题")
        
        # 通用修复建议
        recommendations.extend([
            "检查并修复WebSocket连接健康监控机制",
            "强化时间戳新鲜度检查逻辑",
            "优化跨交易所时间同步容忍度配置", 
            "实现时间戳数据源追踪和调试日志",
            "按照22阈值正确调整.md文档优化相关阈值配置"
        ])
        
        return recommendations

async def main():
    """主诊断流程"""
    print("🔥 时间戳同步问题精确诊断系统启动")
    print("=" * 60)
    
    diagnostic = TimestampSyncDiagnostic()
    
    try:
        # 运行全面诊断
        report = await diagnostic.run_comprehensive_diagnosis()
        
        # 输出诊断报告
        print("\n📋 诊断报告摘要:")
        print(f"目标问题: {report['target_issue']}")
        print(f"诊断时间: {report['diagnosis_timestamp']}")
        print(f"发现问题数: {report['total_issues_found']}")
        print(f"严重程度: {report['severity']}")
        print(f"处理优先级: {report['action_priority']}")
        
        print("\n🚨 关键问题列表:")
        for i, issue in enumerate(report['critical_issues'], 1):
            print(f"  {i}. {issue}")
        
        print("\n💡 修复建议:")
        for i, rec in enumerate(report['fix_recommendations'], 1):
            print(f"  {i}. {rec}")
        
        # 保存详细报告
        import json
        report_file = f"/root/myproject/123/65B 修复了 日志/diagnostic_results/timestamp_sync_diagnosis_{int(time.time())}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细诊断报告已保存: {report_file}")
        print("=" * 60)
        print("🎯 诊断完成！请根据修复建议进行相应修复。")
        
        return report
        
    except Exception as e:
        logger.error(f"诊断过程异常: {str(e)}")
        print(f"❌ 诊断失败: {str(e)}")
        return None

if __name__ == "__main__":
    # 运行诊断
    asyncio.run(main())