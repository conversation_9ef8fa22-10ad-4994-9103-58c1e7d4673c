#!/usr/bin/env python3
"""
Bybit API直接查询诊断
检查ICNT-USDT期货的真实步长设置
"""

import os
import sys
import asyncio
import json

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

async def query_bybit_step_size():
    """直接查询Bybit API获取ICNT-USDT期货步长"""
    try:
        from exchanges.bybit_exchange import BybitExchange
        
        # 获取API密钥
        api_key = os.getenv("BYBIT_API_KEY", "")
        api_secret = os.getenv("BYBIT_API_SECRET", "")
        
        if not api_key or not api_secret:
            print("[错误] 请设置BYBIT_API_KEY和BYBIT_API_SECRET环境变量")
            return
            
        # 创建Bybit交易所实例
        exchange = BybitExchange(api_key, api_secret)
        
        print("[开始] 查询Bybit ICNT-USDT期货交易规则...")
        
        # 直接调用API获取交易对信息
        response = await exchange.get_instruments_info("linear", "ICNTUSDT")
        
        print(f"[响应] API原始返回:")
        print(json.dumps(response, indent=2, ensure_ascii=False))
        
        # 解析关键信息
        if response and "result" in response and "list" in response["result"]:
            instruments = response["result"]["list"]
            if instruments:
                instrument = instruments[0]
                lot_size_filter = instrument.get("lotSizeFilter", {})
                price_filter = instrument.get("priceFilter", {})
                
                print(f"\n[解析] ICNT-USDT期货关键参数:")
                print(f"  qtyStep: {lot_size_filter.get('qtyStep')}")
                print(f"  minOrderQty: {lot_size_filter.get('minOrderQty')}")
                print(f"  maxOrderQty: {lot_size_filter.get('maxOrderQty')}")
                print(f"  tickSize: {price_filter.get('tickSize')}")
                
                # 验证步长值
                qty_step = lot_size_filter.get('qtyStep')
                if qty_step:
                    step_size_float = float(qty_step)
                    print(f"\n[分析] 步长验证:")
                    print(f"  原始qtyStep: {qty_step}")
                    print(f"  浮点数值: {step_size_float}")
                    print(f"  是否 < 0.0001: {step_size_float < 0.0001}")
                    print(f"  166.904是否合法: {166.904 % step_size_float == 0}")
                    
                    # 测试166.904的处理
                    from decimal import Decimal
                    amount_decimal = Decimal("166.904")
                    step_decimal = Decimal(str(step_size_float))
                    truncated = (amount_decimal // step_decimal) * step_decimal
                    
                    print(f"\n[测试] 166.904处理:")
                    print(f"  步长: {step_size_float}")
                    print(f"  截取前: 166.904")
                    print(f"  截取后: {float(truncated)}")
                    print(f"  是否相等: {166.904 == float(truncated)}")
                    
            else:
                print("[错误] 未找到ICNT-USDT期货数据")
        else:
            print("[错误] API响应格式异常")
            
        await exchange.close()
        
    except Exception as e:
        print(f"[异常] 查询失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 加载环境变量
    from dotenv import load_dotenv
    load_dotenv()
    
    asyncio.run(query_bybit_step_size())