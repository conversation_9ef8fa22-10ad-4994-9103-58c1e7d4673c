2025-07-31 18:22:34 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-31 18:22:34 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-31 18:22:34 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-31 18:22:34 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-31 18:22:34.047 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-31 18:22:34.048 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-31 18:22:34 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-31 18:22:34 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-31 18:22:34 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-31 18:22:34 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-31 18:22:34.668 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 18:22:35.164 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:22:35.164 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:22:35.665 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 18:22:35.665 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 18:22:35 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-31 18:22:35.666 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 18:22:36.174 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:22:36.174 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:22:36.673 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 18:22:36.673 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 18:22:36 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-31 18:22:36.673 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 18:22:37.161 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:22:37.161 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:22:37.660 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 18:22:37.660 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 18:22:37 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-31 18:22:37.660 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 18:22:38.182 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:22:38.182 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:22:38.667 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 18:22:38.667 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 18:22:38 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-31 18:22:38 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-31 18:22:39.160 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-31 18:22:40.340 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 18:22:41.852 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 18:22:42.854 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 18:22:43.421 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 18:22:44.843 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 18:22:45.407 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 18:22:45.976 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 18:23:04.565 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:23:04.565 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:23:05.564 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:23:05.564 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:23:06.561 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:23:06.561 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:23:07.564 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:23:07.564 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:23:08.569 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:23:08.569 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:23:09.569 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:23:09.569 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:23:54.531 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-31 18:23:55.033 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 18:23:55.042 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 18:23:55.051 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-07-31 18:23:55.062 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 18:23:57.629 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-07-31 18:23:58.126 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-07-31 18:23:58.129 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-07-31 18:23:58.132 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-07-31 18:23:58.135 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-07-31 18:23:59.137 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 18:23:59.639 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 18:23:59.639 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 18:23:59.639 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 18:23:59.640 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 18:23:59.640 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 18:23:59.640 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 18:23:59.640 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 18:23:59.640 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 18:23:59.640 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-07-31 18:23:59.712 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:23:59.712 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:24:00.211 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:24:00.211 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:24:00.212 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:24:00.212 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:24:00.215 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:24:00.215 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:24:00.216 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-31 18:24:00.216 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:24:00.229 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 18:24:00.229 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 18:24:00.229 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 18:24:00.229 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 18:24:00.230 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 18:24:00.230 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 18:24:00.230 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 18:24:00.230 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 18:24:00.230 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 18:24:00.230 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:24:00.232 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 18:24:00.232 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 18:24:00.232 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 18:24:00.232 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 18:24:00.233 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 18:24:00.233 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 18:24:00.233 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 18:24:00.233 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 18:24:00.233 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 18:24:00.233 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:24:00.234 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 18:24:00.234 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 18:24:00.234 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 18:24:00.234 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 18:24:00.234 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 18:24:00.234 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 18:24:00.234 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 18:24:00.234 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 18:24:00.234 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 18:24:00.234 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:24:00.241 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 18:24:00.241 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 18:24:00.241 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 18:24:00.241 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 18:24:00.241 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 18:24:00.241 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 18:24:00.242 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 18:24:00.242 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 18:24:00.242 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 18:24:00.242 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:24:00.246 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 18:24:00.246 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 18:24:00.250 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-31 18:24:00.250 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-31 18:24:00.250 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-31 18:24:00.250 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-31 18:24:00.250 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-31 18:24:00.250 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-31 18:24:00.250 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 18:24:00.250 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-31 18:24:00.251 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-31 18:24:00.251 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-31 18:24:00.714 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 18:24:00.714 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 18:24:00.715 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 18:24:00.715 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 18:24:00.715 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 18:24:00.715 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 18:24:00.719 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 18:24:00.720 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 18:24:00.720 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 18:24:00.720 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 18:24:00.721 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 18:24:00.721 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 18:24:00.726 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 18:24:00.726 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 18:24:00.727 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 18:24:00.727 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 18:24:00.733 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-31 18:24:00.733 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-31 18:24:11.207 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-31 18:24:12.592 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 18:24:14.105 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 18:24:15.105 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 18:24:15.669 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 18:24:16.178 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 18:24:16.753 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 18:24:17.319 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 18:24:22.136 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-31 18:24:23.660 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-31 18:24:25.159 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 18:24:26.211 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 18:24:26.799 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-31 18:24:27.315 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 18:24:27.973 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-31 18:24:28.624 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
