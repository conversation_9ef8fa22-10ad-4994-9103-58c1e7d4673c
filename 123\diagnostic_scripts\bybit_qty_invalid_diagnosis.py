#!/usr/bin/env python3
"""
Bybit Qty Invalid错误精确诊断脚本
专门诊断 2025-07-31 13:15:09.692 [ERROR] 'Bybit API错误: 10001: Qty invalid' 问题

根据错误信息：
- symbol='ICNT-USDT'  
- quantity='166.904'
- step_size='0.001'
- price_step='0.01'
- market_type='futures'
- exchange_name='bybit'

诊断目标：
1. 精准定位为什么166.904被拒绝
2. 检查step_size处理逻辑
3. 验证数量格式化流程
4. 确认三交易所一致性问题
"""

import os
import sys
import asyncio
import time
from decimal import Decimal, ROUND_DOWN

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class BybitQtyInvalidDiagnosis:
    """Bybit数量无效错误诊断器"""
    
    def __init__(self):
        self.results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_case": {
                "symbol": "ICNT-USDT",
                "quantity": 166.904,
                "step_size": 0.001,
                "price_step": 0.01,
                "market_type": "futures",
                "exchange": "bybit"
            },
            "diagnosis_results": {}
        }
    
    def analyze_step_size_truncation(self):
        """分析步长截取逻辑"""
        print("[步骤1] 分析步长截取逻辑")
        
        quantity = self.results["test_case"]["quantity"]
        step_size = self.results["test_case"]["step_size"]
        
        # 模拟trading_rules_preloader的截取逻辑
        amount_decimal = Decimal(str(quantity))
        step_decimal = Decimal(str(step_size))
        
        # 截取到步长的整数倍
        truncated = (amount_decimal // step_decimal) * step_decimal
        result = float(truncated)
        
        # 计算截取差异
        difference = quantity - result
        difference_pct = (difference / quantity) * 100 if quantity > 0 else 0
        
        analysis = {
            "original_quantity": quantity,
            "step_size": step_size,
            "truncated_quantity": result,
            "difference": difference,
            "difference_percentage": difference_pct,
            "steps_count": int(amount_decimal // step_decimal),
            "is_valid_multiple": (amount_decimal % step_decimal) == 0
        }
        
        print(f"   原始数量: {quantity}")
        print(f"   步长: {step_size}")
        print(f"   截取后: {result}")
        print(f"   差异: {difference:.6f} ({difference_pct:.3f}%)")
        print(f"   步数: {analysis['steps_count']}")
        print(f"   是否步长整数倍: {analysis['is_valid_multiple']}")
        
        self.results["diagnosis_results"]["step_size_analysis"] = analysis
        return analysis
    
    def check_bybit_precision_rules(self):
        """检查Bybit精度规则"""
        print("\n[步骤2] 检查Bybit精度规则")
        
        step_size = self.results["test_case"]["step_size"]
        
        # 检查是否触发保守默认值逻辑
        is_high_precision = step_size < 0.0001
        should_use_conservative = is_high_precision
        conservative_value = 0.01  # futures默认值
        
        analysis = {
            "current_step_size": step_size,
            "is_high_precision": is_high_precision,
            "threshold": 0.0001,
            "should_use_conservative": should_use_conservative,
            "conservative_default": conservative_value if should_use_conservative else step_size
        }
        
        print(f"   当前步长: {step_size}")
        print(f"   是否高精度: {is_high_precision}")
        print(f"   阈值: 0.0001")
        print(f"   是否使用保守值: {should_use_conservative}")
        print(f"   最终步长: {analysis['conservative_default']}")
        
        self.results["diagnosis_results"]["precision_rules"] = analysis
        return analysis
    
    def simulate_bybit_api_validation(self):
        """模拟Bybit API验证逻辑"""
        print("\n[步骤3] 模拟Bybit API验证逻辑")
        
        quantity = 166.904
        step_size = 0.001
        
        # 检查数量是否符合步长要求
        remainder = Decimal(str(quantity)) % Decimal(str(step_size))
        is_valid_step = remainder == 0
        
        # 检查数量格式
        quantity_str = str(quantity)
        decimal_places = len(quantity_str.split('.')[-1]) if '.' in quantity_str else 0
        step_decimal_places = len(str(step_size).split('.')[-1]) if '.' in str(step_size) else 0
        
        analysis = {
            "quantity": quantity,
            "step_size": step_size,
            "remainder": float(remainder),
            "is_valid_step_multiple": is_valid_step,
            "quantity_decimal_places": decimal_places,
            "step_decimal_places": step_decimal_places,
            "quantity_string": quantity_str,
            "possible_rejection_reasons": []
        }
        
        # 分析可能的拒绝原因
        if not is_valid_step:
            analysis["possible_rejection_reasons"].append(f"数量{quantity}不是步长{step_size}的整数倍")
        
        if decimal_places > step_decimal_places:
            analysis["possible_rejection_reasons"].append(f"数量精度{decimal_places}位超过步长精度{step_decimal_places}位")
        
        print(f"   数量: {quantity}")
        print(f"   步长: {step_size}")
        print(f"   余数: {remainder}")
        print(f"   是否合法倍数: {is_valid_step}")
        print(f"   数量小数位: {decimal_places}")
        print(f"   步长小数位: {step_decimal_places}")
        print(f"   可能拒绝原因: {analysis['possible_rejection_reasons']}")
        
        self.results["diagnosis_results"]["api_validation"] = analysis
        return analysis
    
    def check_correct_quantity_formatting(self):
        """检查正确的数量格式化"""
        print("\n[步骤4] 检查正确的数量格式化")
        
        quantity = 166.904
        step_size = 0.001
        
        # 正确的截取方式
        amount_decimal = Decimal(str(quantity))
        step_decimal = Decimal(str(step_size))
        
        truncated = (amount_decimal // step_decimal) * step_decimal
        correct_quantity = float(truncated)
        
        # 验证截取后的数量
        remainder_after = Decimal(str(correct_quantity)) % step_decimal
        is_valid_after = remainder_after == 0
        
        analysis = {
            "original": quantity,
            "step_size": step_size,
            "correct_truncated": correct_quantity,
            "difference": quantity - correct_quantity,
            "is_valid_after_truncation": is_valid_after,
            "remainder_after": float(remainder_after)
        }
        
        print(f"   原始数量: {quantity}")
        print(f"   正确截取后: {correct_quantity}")
        print(f"   差异: {analysis['difference']:.6f}")
        print(f"   截取后是否合法: {is_valid_after}")
        
        self.results["diagnosis_results"]["correct_formatting"] = analysis
        return analysis
    
    def test_actual_trading_rules_preloader(self):
        """测试实际的交易规则预加载器"""
        print("\n[步骤5] 测试实际的交易规则预加载器")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试截取方法
            result = preloader.truncate_to_step_size(
                166.904, "bybit", "ICNT-USDT", "futures"
            )
            
            analysis = {
                "preloader_available": True,
                "input_quantity": 166.904,
                "output_quantity": result,
                "difference": 166.904 - result if result else None,
                "success": result is not None
            }
            
            print(f"   输入数量: 166.904")
            print(f"   输出数量: {result}")
            print(f"   差异: {analysis['difference']}")
            print(f"   处理成功: {analysis['success']}")
            
        except Exception as e:
            analysis = {
                "preloader_available": False,
                "error": str(e),
                "success": False
            }
            print(f"   ❌ 无法测试预加载器: {e}")
        
        self.results["diagnosis_results"]["preloader_test"] = analysis
        return analysis
    
    def generate_recommendations(self):
        """生成修复建议"""
        print("\n[步骤6] 生成修复建议")
        
        recommendations = []
        
        # 分析诊断结果
        step_analysis = self.results["diagnosis_results"].get("step_size_analysis", {})
        api_validation = self.results["diagnosis_results"].get("api_validation", {})
        
        if not step_analysis.get("is_valid_multiple"):
            recommendations.append({
                "priority": "HIGH",
                "issue": "数量不是步长的整数倍",
                "solution": f"将166.904截取为{step_analysis.get('truncated_quantity', 166)}",
                "location": "trading_rules_preloader.py -> truncate_to_step_size方法"
            })
        
        if api_validation.get("possible_rejection_reasons"):
            for reason in api_validation["possible_rejection_reasons"]:
                recommendations.append({
                    "priority": "HIGH", 
                    "issue": reason,
                    "solution": "确保数量格式化符合Bybit API要求",
                    "location": "bybit_exchange.py -> create_futures_order方法"
                })
        
        print("   [修复建议]:")
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. [{rec['priority']}] {rec['issue']}")
            print(f"      解决方案: {rec['solution']}")
            print(f"      修复位置: {rec['location']}")
            print()
        
        self.results["recommendations"] = recommendations
        return recommendations
    
    def run_diagnosis(self):
        """运行完整诊断"""
        print("[开始] Bybit Qty Invalid错误精确诊断")
        print("=" * 60)
        
        # 执行所有诊断步骤
        self.analyze_step_size_truncation()
        self.check_bybit_precision_rules()
        self.simulate_bybit_api_validation()
        self.check_correct_quantity_formatting()
        self.test_actual_trading_rules_preloader()
        self.generate_recommendations()
        
        print("\n[完成] 诊断完成")
        
        # 输出关键发现
        print("\n[关键发现]:")
        step_analysis = self.results["diagnosis_results"].get("step_size_analysis", {})
        if not step_analysis.get("is_valid_multiple"):
            print(f"   [错误] 数量166.904不是步长0.001的整数倍")
            print(f"   [建议] 应该截取为: {step_analysis.get('truncated_quantity')}")
        
        api_validation = self.results["diagnosis_results"].get("api_validation", {})
        if api_validation.get("possible_rejection_reasons"):
            print(f"   [错误] API拒绝原因: {', '.join(api_validation['possible_rejection_reasons'])}")
        
        return self.results

def main():
    """主函数"""
    print("Bybit Qty Invalid错误诊断工具")
    print("诊断案例: ICNT-USDT 166.904数量被拒绝问题")
    print()
    
    diagnosis = BybitQtyInvalidDiagnosis()
    results = diagnosis.run_diagnosis()
    
    # 保存诊断结果
    import json
    output_file = "diagnostic_results/bybit_qty_invalid_diagnosis_results.json"
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n[保存] 诊断结果已保存到: {output_file}")
    
    return results

if __name__ == "__main__":
    main()